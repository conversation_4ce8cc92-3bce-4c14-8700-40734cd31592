#!/usr/bin/env python3
"""
Test script for YOLO inference with geographic coordinate conversion.

This script tests the yolo_inference.py module with available data.
"""

import sys
from pathlib import Path
import geopandas as gpd

# Add current directory to path to import yolo_inference
sys.path.append(str(Path(__file__).parent))

from yolo_inference import yolo_inference_to_shp


def find_test_files():
    """Find available test files in the project."""
    project_root = Path(__file__).parent.parent
    
    # Look for YOLO model weights
    model_candidates = [
        project_root / "runs" / "detect" / "train4" / "weights" / "best.pt",
        project_root / "yolo11n.pt",
        project_root / "yolo11s.pt",
    ]
    
    model_path = None
    for candidate in model_candidates:
        if candidate.exists():
            model_path = candidate
            break
    
    # Look for TIFF images
    image_candidates = [
        project_root / "temp" / "tod_P061301_2.tif",
        project_root / "temp" / "tod_P061301_3.tif",
        project_root / "temp" / "tod_P061302_8.tif",
    ]
    
    image_path = None
    for candidate in image_candidates:
        if candidate.exists():
            image_path = candidate
            break
    
    return model_path, image_path


def test_inference():
    """Test the YOLO inference function."""
    print("Testing YOLO inference with geographic coordinate conversion...")
    print("=" * 60)
    
    # Find test files
    model_path, image_path = find_test_files()
    
    if model_path is None:
        print("❌ No YOLO model found. Please ensure you have:")
        print("   - runs/detect/train4/weights/best.pt (trained model)")
        print("   - yolo11n.pt or yolo11s.pt (pretrained models)")
        return False
    
    if image_path is None:
        print("❌ No TIFF image found. Please ensure you have TIFF files in the temp/ directory.")
        return False
    
    print(f"✓ Found model: {model_path}")
    print(f"✓ Found image: {image_path}")
    
    # Set up output path
    output_dir = Path(__file__).parent.parent / "output" / "test_detections"
    output_dir.mkdir(parents=True, exist_ok=True)
    output_path = output_dir / f"{image_path.stem}_test_detections.shp"
    
    print(f"✓ Output will be saved to: {output_path}")
    print("-" * 60)
    
    try:
        # Run inference with conservative settings for testing
        yolo_inference_to_shp(
            model_path=str(model_path),
            img_path=str(image_path),
            output_shp_path=str(output_path),
            block_size=512,           # Smaller blocks for faster testing
            overlap_ratio=0.1,
            conf_threshold=0.3,
            iou_threshold=0.5,
            nms_iou_threshold=0.5,
            output_format="center",
            verbose=True
        )
        
        # Verify output
        if output_path.exists():
            print("✓ Shapefile created successfully!")
            
            # Load and inspect the results
            gdf = gpd.read_file(output_path)
            print(f"✓ Loaded shapefile with {len(gdf)} detections")
            
            if len(gdf) > 0:
                print(f"✓ Confidence range: {gdf['confidence'].min():.3f} - {gdf['confidence'].max():.3f}")
                print(f"✓ Coordinate system: {gdf.crs}")
                print(f"✓ Geometry type: {gdf.geometry.geom_type.iloc[0]}")
                
                # Show first few detections
                print("\nFirst 3 detections:")
                for i, row in gdf.head(3).iterrows():
                    print(f"  Detection {i+1}: confidence={row['confidence']:.3f}, "
                          f"class={row['class']}, geometry={row['geometry']}")
            else:
                print("⚠️  No detections found (this might be normal depending on the image content)")
            
            return True
        else:
            print("❌ Shapefile was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_coordinate_conversion():
    """Test coordinate conversion functions."""
    print("\nTesting coordinate conversion functions...")
    print("-" * 40)
    
    try:
        import numpy as np
        import rasterio
        from yolo_inference import convert_to_geographic_coordinates
        
        # Create a simple test transform (identity transform for testing)
        transform = rasterio.Affine(1.0, 0.0, 0.0, 0.0, -1.0, 100.0)
        
        # Test bounding boxes in pixel coordinates
        test_bboxes = np.array([
            [10, 10, 20, 20],
            [50, 50, 60, 60],
            [100, 100, 110, 110]
        ])
        
        # Test center point conversion
        center_geometries = convert_to_geographic_coordinates(test_bboxes, transform, "center")
        print(f"✓ Converted {len(center_geometries)} bboxes to center points")
        
        # Test bbox polygon conversion
        bbox_geometries = convert_to_geographic_coordinates(test_bboxes, transform, "bbox")
        print(f"✓ Converted {len(bbox_geometries)} bboxes to polygons")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in coordinate conversion test: {e}")
        return False


def main():
    """Run all tests."""
    print("YOLO Inference Test Suite")
    print("=" * 60)
    
    # Test coordinate conversion
    coord_test_passed = test_coordinate_conversion()
    
    # Test full inference pipeline
    inference_test_passed = test_inference()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"  Coordinate conversion: {'✓ PASSED' if coord_test_passed else '❌ FAILED'}")
    print(f"  Full inference pipeline: {'✓ PASSED' if inference_test_passed else '❌ FAILED'}")
    
    if coord_test_passed and inference_test_passed:
        print("\n🎉 All tests passed! The YOLO inference script is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
    
    return coord_test_passed and inference_test_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
