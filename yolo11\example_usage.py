#!/usr/bin/env python3
"""
Example usage of YOLO inference script for tobacco detection.

This script demonstrates how to use the yolo_inference.py module to:
1. Run YOLO inference on a TIFF image
2. Convert detection results to geographic coordinates
3. Save results as a shapefile

Make sure you have the required dependencies installed:
- ultralytics
- rasterio
- geopandas
- shapely
- numpy
"""

from pathlib import Path
from yolo_inference import yolo_inference_to_shp


def main():
    # Configuration - Update these paths according to your setup
    model_path = r"C:\Users\<USER>\source\tobacco_det\runs\detect\train4\weights\best.pt"
    image_path = r"C:\Users\<USER>\source\tobacco_det\temp\tod_P061301_2.tif"
    output_path = r"C:\Users\<USER>\source\tobacco_det\output\detections\tod_P061301_2_detections.shp"
    
    # Ensure paths exist
    if not Path(model_path).exists():
        print(f"Error: Model file not found: {model_path}")
        print("Please update the model_path variable with the correct path to your YOLO weights.")
        return
    
    if not Path(image_path).exists():
        print(f"Error: Image file not found: {image_path}")
        print("Please update the image_path variable with the correct path to your TIFF image.")
        return
    
    print("Starting YOLO inference with geographic coordinate conversion...")
    print(f"Model: {model_path}")
    print(f"Image: {image_path}")
    print(f"Output: {output_path}")
    print("-" * 60)
    
    # Run inference with custom parameters
    yolo_inference_to_shp(
        model_path=model_path,
        img_path=image_path,
        output_shp_path=output_path,
        block_size=1024,           # Process in 1024x1024 pixel blocks
        overlap_ratio=0.1,         # 10% overlap between blocks
        conf_threshold=0.3,        # Confidence threshold for detections
        iou_threshold=0.5,         # IoU threshold for YOLO inference
        nms_iou_threshold=0.5,     # IoU threshold for Non-Maximum Suppression
        output_format="center",    # Output center points (use "bbox" for polygons)
        verbose=True               # Print progress information
    )
    
    print("-" * 60)
    print("Inference completed!")
    print(f"Results saved to: {output_path}")
    
    # Check if output file was created
    if Path(output_path).exists():
        print("✓ Shapefile created successfully")
        
        # Print some basic info about the results
        try:
            import geopandas as gpd
            gdf = gpd.read_file(output_path)
            print(f"✓ Found {len(gdf)} detections")
            if len(gdf) > 0:
                print(f"✓ Confidence range: {gdf['confidence'].min():.3f} - {gdf['confidence'].max():.3f}")
                print(f"✓ Coordinate system: {gdf.crs}")
        except Exception as e:
            print(f"Warning: Could not read shapefile for verification: {e}")
    else:
        print("✗ Shapefile was not created")


def example_batch_processing():
    """
    Example of how to process multiple images in batch.
    """
    # Configuration
    model_path = r"C:\Users\<USER>\source\tobacco_det\runs\detect\train4\weights\best.pt"
    input_dir = Path(r"C:\Users\<USER>\source\tobacco_det\temp")
    output_dir = Path(r"C:\Users\<USER>\source\tobacco_det\output\detections")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find all TIFF files
    tiff_files = list(input_dir.glob("*.tif"))
    
    if not tiff_files:
        print(f"No TIFF files found in {input_dir}")
        return
    
    print(f"Found {len(tiff_files)} TIFF files to process")
    
    # Process each file
    for i, tiff_file in enumerate(tiff_files, 1):
        print(f"\nProcessing {i}/{len(tiff_files)}: {tiff_file.name}")
        
        output_shp = output_dir / f"{tiff_file.stem}_detections.shp"
        
        try:
            yolo_inference_to_shp(
                model_path=model_path,
                img_path=str(tiff_file),
                output_shp_path=str(output_shp),
                block_size=1024,
                overlap_ratio=0.1,
                conf_threshold=0.3,
                iou_threshold=0.5,
                nms_iou_threshold=0.5,
                output_format="center",
                verbose=False  # Reduce verbosity for batch processing
            )
            print(f"✓ Completed: {output_shp}")
            
        except Exception as e:
            print(f"✗ Error processing {tiff_file.name}: {e}")
    
    print(f"\nBatch processing completed. Results saved to: {output_dir}")


if __name__ == "__main__":
    # Run single file example
    main()
    
    # Uncomment the line below to run batch processing example instead
    # example_batch_processing()
