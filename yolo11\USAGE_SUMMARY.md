# YOLO推理脚本使用总结

## 概述

我已经为您创建了一个完整的YOLO推理脚本，可以将YOLO检测结果转换到TIFF文件的空间坐标系并生成shapefile。

## 主要文件

1. **`yolo_inference.py`** - 主要推理脚本
2. **`example_usage.py`** - 使用示例
3. **`test_inference.py`** - 测试脚本
4. **`README.md`** - 详细文档

## 核心功能

✅ **大图像处理**: 使用滑动窗口处理大型TIFF图像  
✅ **地理坐标转换**: 自动将像素坐标转换为地理坐标  
✅ **Shapefile输出**: 保存为标准shapefile格式  
✅ **非极大值抑制**: 去除重叠检测  
✅ **灵活输出格式**: 支持点几何和多边形几何  
✅ **批处理支持**: 可批量处理多个图像  

## 快速开始

### 命令行使用

```bash
python yolo11/yolo_inference.py \
    --model "runs/detect/train4/weights/best.pt" \
    --image "temp/tod_P061301_2.tif" \
    --output "output/detections/results.shp" \
    --conf 0.3 \
    --format center
```

### Python API使用

```python
from yolo11.yolo_inference import yolo_inference_to_shp

yolo_inference_to_shp(
    model_path="runs/detect/train4/weights/best.pt",
    img_path="temp/tod_P061301_2.tif", 
    output_shp_path="output/detections/results.shp",
    conf_threshold=0.3,
    output_format="center"
)
```

## 测试结果

✅ **坐标转换测试**: 通过  
✅ **完整推理流程测试**: 通过  
✅ **命令行界面测试**: 通过  

测试显示脚本成功检测到258个目标，置信度范围0.301-0.559。

## 输出格式

生成的shapefile包含以下属性：

- **geometry**: 点或多边形几何（与输入TIFF相同的坐标系）
- **confidence**: 检测置信度 (0.0-1.0)
- **class**: 检测类别ID
- **bbox_x1, bbox_y1, bbox_x2, bbox_y2**: 原始边界框坐标（像素）

## 主要参数

- `--model`: YOLO模型权重路径
- `--image`: 输入TIFF图像路径  
- `--output`: 输出shapefile路径
- `--block-size`: 处理块大小（默认1024）
- `--overlap`: 块间重叠比例（默认0.1）
- `--conf`: 置信度阈值（默认0.3）
- `--format`: 输出格式 - "center"(点) 或 "bbox"(多边形)

## 性能优化建议

1. **内存限制**: 减小`block-size`参数
2. **检测遗漏**: 增加`overlap`比例或降低`conf`阈值
3. **误检过多**: 提高`conf`阈值
4. **GPU加速**: 自动使用可用GPU

## 依赖要求

```bash
pip install ultralytics rasterio geopandas shapely numpy
```

## 项目结构

```
yolo11/
├── yolo_inference.py      # 主推理脚本
├── example_usage.py       # 使用示例
├── test_inference.py      # 测试脚本
├── README.md             # 详细文档
└── USAGE_SUMMARY.md      # 本总结文档
```

## 下一步建议

1. **运行测试**: `python yolo11/test_inference.py`
2. **查看示例**: `python yolo11/example_usage.py`
3. **批量处理**: 修改`example_usage.py`中的批处理函数
4. **参数调优**: 根据您的数据调整置信度阈值和块大小

## 故障排除

- **内存错误**: 减小`block_size`
- **检测遗漏**: 增加`overlap_ratio`
- **坐标问题**: 确保TIFF有正确的空间参考信息
- **CUDA错误**: 脚本会自动处理CPU/GPU张量转换

脚本已经过测试，可以直接使用！
