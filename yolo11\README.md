# YOLO Inference with Geographic Coordinate Conversion

This module provides functionality to run YOLO inference on TIFF images and convert the detection results to geographic coordinates, saving them as shapefiles.

## Features

- **Large Image Processing**: Handles large TIFF images by processing them in sliding windows/blocks
- **Geographic Coordinate Conversion**: Automatically converts pixel coordinates to geographic coordinates using the TIFF's spatial reference information
- **Shapefile Output**: Saves detection results as shapefiles with proper coordinate reference system
- **Non-Maximum Suppression**: Removes overlapping detections across block boundaries
- **Flexible Output Formats**: Supports both point geometries (center points) and polygon geometries (bounding boxes)
- **Batch Processing**: Can process multiple images in batch mode

## Requirements

Install the required dependencies:

```bash
pip install ultralytics rasterio geopandas shapely numpy
```

## Usage

### Command Line Interface

```bash
python yolo_inference.py --model path/to/model.pt --image path/to/image.tif --output path/to/output.shp
```

#### Command Line Arguments

- `--model`: Path to YOLO model weights (.pt file)
- `--image`: Path to input TIFF image
- `--output`: Path to output shapefile
- `--block-size`: Processing block size in pixels (default: 1024)
- `--overlap`: Overlap ratio between blocks (default: 0.1)
- `--conf`: Confidence threshold for detections (default: 0.3)
- `--iou`: IoU threshold for YOLO inference (default: 0.5)
- `--nms-iou`: IoU threshold for Non-Maximum Suppression (default: 0.5)
- `--format`: Output format - "center" for points or "bbox" for polygons (default: "center")
- `--quiet`: Suppress verbose output

#### Example Command

```bash
python yolo_inference.py \
    --model "runs/detect/train4/weights/best.pt" \
    --image "temp/tod_P061301_2.tif" \
    --output "output/detections/tod_P061301_2_detections.shp" \
    --block-size 1024 \
    --overlap 0.1 \
    --conf 0.3 \
    --format center
```

### Python API

```python
from yolo_inference import yolo_inference_to_shp

# Run inference
yolo_inference_to_shp(
    model_path="path/to/model.pt",
    img_path="path/to/image.tif",
    output_shp_path="path/to/output.shp",
    block_size=1024,
    overlap_ratio=0.1,
    conf_threshold=0.3,
    iou_threshold=0.5,
    nms_iou_threshold=0.5,
    output_format="center",
    verbose=True
)
```

### Example Usage

See `example_usage.py` for complete examples including:
- Single image processing
- Batch processing multiple images
- Error handling and validation

```python
python example_usage.py
```

## Parameters

### Processing Parameters

- **block_size**: Size of processing blocks in pixels. Larger blocks use more memory but may be more efficient. Smaller blocks are better for limited memory.
- **overlap_ratio**: Overlap between adjacent blocks (0.0 to 1.0). Higher overlap helps detect objects at block boundaries but increases processing time.

### Detection Parameters

- **conf_threshold**: Minimum confidence score for detections (0.0 to 1.0)
- **iou_threshold**: IoU threshold for YOLO's built-in NMS during inference
- **nms_iou_threshold**: IoU threshold for post-processing NMS across block boundaries

### Output Parameters

- **output_format**: 
  - "center": Creates point geometries at detection centers
  - "bbox": Creates polygon geometries representing full bounding boxes

## Output Shapefile

The output shapefile contains the following attributes:

- **geometry**: Point or Polygon geometry in the same CRS as the input TIFF
- **confidence**: Detection confidence score (0.0 to 1.0)
- **class**: Detected class ID (integer)
- **bbox_x1, bbox_y1, bbox_x2, bbox_y2**: Original bounding box coordinates in pixels

## How It Works

1. **Image Loading**: Opens the TIFF image with rasterio to extract spatial reference information
2. **Block Creation**: Divides the large image into overlapping blocks for processing
3. **YOLO Inference**: Runs YOLO inference on each block
4. **Coordinate Conversion**: Converts pixel coordinates to geographic coordinates using the TIFF's affine transform
5. **Non-Maximum Suppression**: Removes duplicate detections across block boundaries
6. **Shapefile Creation**: Creates a GeoDataFrame and saves it as a shapefile

## Tips for Best Results

1. **Block Size**: Use larger blocks (1024-2048) for better detection accuracy, smaller blocks (512-1024) for memory constraints
2. **Overlap**: Use 10-20% overlap to ensure detections at block boundaries are not missed
3. **Confidence Threshold**: Start with 0.3 and adjust based on your model's performance
4. **Memory Management**: For very large images, reduce block size or process fewer blocks at once

## Troubleshooting

### Common Issues

1. **Memory Errors**: Reduce block_size parameter
2. **Missing Detections**: Increase overlap_ratio or reduce conf_threshold
3. **Too Many False Positives**: Increase conf_threshold or adjust nms_iou_threshold
4. **Coordinate Issues**: Ensure your TIFF has proper spatial reference information

### Error Messages

- "No spatial reference found": TIFF file lacks coordinate system information
- "Model file not found": Check the path to your YOLO weights file
- "Image file not found": Check the path to your input TIFF file

## Performance Notes

- Processing time scales with image size and number of detections
- GPU acceleration is automatically used if available
- Memory usage depends on block_size parameter
- Larger overlap ratios increase processing time but improve detection completeness
