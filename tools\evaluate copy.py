#!/usr/bin/env python3
"""
Point Detection Evaluation Script

This script evaluates point detection results by:
1. Loading inference results (Point geometries in UTM coordinates)
2. Loading ground truth (bbox/polygon geometries, using their centers)
3. Reprojecting ground truth to match inference CRS (UTM)
4. Using Hungarian algorithm to match points
5. Calculating mean distance and counting missed points
6. Discarding point pairs with distance > 0.15m (15cm)

Usage:
    python evaluate.py <inference_shp> <ground_truth_shp>
"""

import argparse
import sys
import warnings
from pathlib import Path
from typing import Any, Dict,  Tuple

import geopandas as gpd
import numpy as np
from scipy.optimize import linear_sum_assignment

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

target_crs = "EPSG:32647"


def load_inference_points(inference_shp_path: str) -> gpd.GeoSeries:
    """
    Load inference results from shapefile.

    Args:
        inference_shp_path: Path to inference shapefile containing Point geometries

    Returns:
        GeoDataFrame with Point geometries in UTM coordinates
    """
    print(f"Loading inference results from: {inference_shp_path}")

    # Load inference shapefile
    inference_gdf = gpd.read_file(inference_shp_path).to_crs(target_crs)
    inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]

    print(f"Loaded {len(inference_gdf)} inference points")
    print(f"Inference CRS: {inference_gdf.crs}")

    # Verify all geometries are Points
    non_points = inference_gdf[~inference_gdf.geometry.geom_type.eq("Point")]
    if len(non_points) > 0:
        print(f"Warning: Found {len(non_points)} non-Point geometries in inference data")
        inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]
        print(f"Filtered to {len(inference_gdf)} Point geometries")

    return inference_gdf.geometry


def load_ground_truth_points(gt_shp_path: str, target_crs: Any) -> gpd.GeoSeries:
    """
    Load ground truth from shapefile and convert to center points.

    Args:
        gt_shp_path: Path to ground truth shapefile containing bbox/polygon geometries
        target_crs: Target CRS to reproject to (should match inference CRS)

    Returns:
        GeoDataFrame with Point geometries representing centers of ground truth bboxes
    """
    print(f"Loading ground truth from: {gt_shp_path}")

    # Load ground truth shapefile
    gt_gdf = gpd.read_file(gt_shp_path).to_crs(target_crs)
    gt_gdf = gt_gdf[gt_gdf.geometry.geom_type.eq("Polygon")]

    print(f"Loaded {len(gt_gdf)} ground truth geometries")
    print(f"Ground truth CRS: {gt_gdf.crs}")

    # Convert geometries to center points
    print("Converting ground truth geometries to center points...")
    gt_points_gdf = gt_gdf.centroid

    print(f"Created {len(gt_points_gdf)} ground truth center points")

    return gt_points_gdf


def calculate_distance_matrix(inference_points: gpd.GeoSeries, gt_points: gpd.GeoSeries) -> np.ndarray:
    """
    Calculate distance matrix between inference points and ground truth points.

    Args:
        inference_points: GeoSeries with inference Point geometries
        gt_points: GeoSeries with ground truth Point geometries

    Returns:
        Distance matrix of shape (n_inference, n_gt) in meters
    """
    print("Calculating distance matrix...")

    # Extract coordinates
    # inf_coords = np.array([[point.x, point.y] for point in inference_points])
    # gt_coords = np.array([[point.x, point.y] for point in gt_points])
    inf_coords = np.array([inference_points.x, inference_points.y]).T
    gt_coords = np.array([gt_points.x, gt_points.y]).T

    # Calculate pairwise distances using broadcasting
    # inf_coords: (n_inf, 2), gt_coords: (n_gt, 2)
    # Result: (n_inf, n_gt)
    inf_expanded = inf_coords[:, np.newaxis, :]  # (n_inf, 1, 2)
    gt_expanded = gt_coords[np.newaxis, :, :]  # (1, n_gt, 2)

    # Euclidean distance
    distances = np.sqrt(np.sum((inf_expanded - gt_expanded) ** 2, axis=2))

    print(f"Distance matrix shape: {distances.shape}")
    print(f"Min distance: {distances.min():.3f}m, Max distance: {distances.max():.3f}m")

    return distances


def hungarian_matching(
    distance_matrix: np.ndarray, max_distance: float = 0.15
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Perform Hungarian algorithm matching with distance threshold.

    Args:
        distance_matrix: Distance matrix of shape (n_inference, n_gt)
        max_distance: Maximum allowed distance for valid matches (in meters)

    Returns:
        Tuple of (matched_inf_indices, matched_gt_indices, matched_distances)
    """
    print(f"Performing Hungarian matching with max distance threshold: {max_distance}m")

    # Apply Hungarian algorithm
    inf_indices, gt_indices = linear_sum_assignment(distance_matrix)

    # Get distances for matched pairs
    matched_distances = distance_matrix[inf_indices, gt_indices]

    # Filter out matches that exceed the distance threshold
    valid_matches = matched_distances <= max_distance

    matched_inf_indices = inf_indices[valid_matches]
    matched_gt_indices = gt_indices[valid_matches]
    matched_distances = matched_distances[valid_matches]

    print(f"Total possible matches: {len(inf_indices)}")
    print(f"Valid matches (≤{max_distance}m): {len(matched_distances)}")
    print(f"Discarded matches (>{max_distance}m): {len(inf_indices) - len(matched_distances)}")

    return matched_inf_indices, matched_gt_indices, matched_distances


def evaluate_detection(inference_shp_path: str, gt_shp_path: str, max_distance: float = 0.15) -> Dict[str, Any]:
    """
    Evaluate point detection performance.

    Args:
        inference_shp_path: Path to inference shapefile
        gt_shp_path: Path to ground truth shapefile
        max_distance: Maximum distance threshold for valid matches (meters)

    Returns:
        Dictionary containing evaluation metrics
    """
    print("=" * 60)
    print("POINT DETECTION EVALUATION")
    print("=" * 60)

    # Load inference points
    inference_gdf = load_inference_points(inference_shp_path)

    # Load ground truth and convert to points
    gt_gdf = load_ground_truth_points(gt_shp_path, inference_gdf.crs)

    # Check if we have data to evaluate
    if len(inference_gdf) == 0:
        print("Error: No inference points found!")
        return {"error": "No inference points"}

    if len(gt_gdf) == 0:
        print("Error: No ground truth points found!")
        return {"error": "No ground truth points"}

    print(f"\nEvaluation setup:")
    print(f"  Inference points: {len(inference_gdf)}")
    print(f"  Ground truth points: {len(gt_gdf)}")
    print(f"  Max distance threshold: {max_distance}m\n")

    # Calculate distance matrix
    distance_matrix = calculate_distance_matrix(inference_gdf, gt_gdf)

    # Perform Hungarian matching
    matched_inf_idx, matched_gt_idx, matched_distances = hungarian_matching(distance_matrix, max_distance)

    # Calculate metrics
    n_inference = len(inference_gdf)
    n_ground_truth = len(gt_gdf)
    n_matched = len(matched_distances)
    n_missed_gt = n_ground_truth - n_matched
    n_false_positives = n_inference - n_matched

    # Calculate mean distance for valid matches
    mean_distance = np.mean(matched_distances) if n_matched > 0 else 0.0

    # Calculate precision, recall, F1
    precision = n_matched / n_inference if n_inference > 0 else 0.0
    recall = n_matched / n_ground_truth if n_ground_truth > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

    # Prepare results
    results = {
        "n_inference": n_inference,
        "n_ground_truth": n_ground_truth,
        "n_matched": n_matched,
        "n_missed_gt": n_missed_gt,
        "n_false_positives": n_false_positives,
        "mean_distance": mean_distance,
        "max_distance_threshold": max_distance,
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        "matched_distances": matched_distances.tolist() if n_matched > 0 else [],
    }

    return results


def print_evaluation_results(results: Dict[str, Any]) -> None:
    """
    Print evaluation results in a formatted way.

    Args:
        results: Dictionary containing evaluation metrics
    """
    if "error" in results:
        print(f"Evaluation failed: {results['error']}")
        return

    print("\n" + "=" * 60)
    print("EVALUATION RESULTS")
    print("=" * 60)

    print(f"Dataset Statistics:")
    print(f"  Inference points:     {results['n_inference']:6d}")
    print(f"  Ground truth points:  {results['n_ground_truth']:6d}")
    print()

    print(f"Matching Results (max distance: {results['max_distance_threshold']}m):")
    print(f"  Matched pairs:        {results['n_matched']:6d}")
    print(f"  Missed GT points:     {results['n_missed_gt']:6d}")
    print(f"  False positives:      {results['n_false_positives']:6d}")
    print()

    print(f"Distance Statistics:")
    if results["n_matched"] > 0:
        print(f"  Mean distance:        {results['mean_distance']:6.3f}m")
        distances = np.array(results["matched_distances"])
        print(f"  Min distance:         {distances.min():6.3f}m")
        print(f"  Max distance:         {distances.max():6.3f}m")
        print(f"  Std distance:         {distances.std():6.3f}m")
    else:
        print(f"  No valid matches found")
    print()

    print(f"Performance Metrics:")
    print(f"  Precision:            {results['precision']:6.3f} ({results['precision'] * 100:5.1f}%)")
    print(f"  Recall:               {results['recall']:6.3f} ({results['recall'] * 100:5.1f}%)")
    print(f"  F1-Score:             {results['f1_score']:6.3f} ({results['f1_score'] * 100:5.1f}%)")
    print()


def main():
    """Main function to run the evaluation."""
    parser = argparse.ArgumentParser(description="Evaluate point detection results using Hungarian algorithm matching")
    parser.add_argument("inference_shp", help="Path to inference shapefile (Point geometries)")
    parser.add_argument("ground_truth_shp", help="Path to ground truth shapefile (bbox/polygon geometries)")
    parser.add_argument(
        "--max-distance",
        type=float,
        default=0.2,
        help="Maximum distance threshold for valid matches in meters (default: 0.15)",
    )

    args = parser.parse_args()

    # Check if files exist
    if not Path(args.inference_shp).exists():
        print(f"Error: Inference shapefile not found: {args.inference_shp}")
        sys.exit(1)

    if not Path(args.ground_truth_shp).exists():
        print(f"Error: Ground truth shapefile not found: {args.ground_truth_shp}")
        sys.exit(1)

    # Run evaluation
    results = evaluate_detection(args.inference_shp, args.ground_truth_shp, args.max_distance)

    # Print results
    print_evaluation_results(results)

if __name__ == "__main__":
    main()
